import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';

Widget widgetTitle(
  String title,
  ReadingSettings currentSetting, {
  Color? textColor,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceS),
    child: Row(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18, // Keep existing font size for visual consistency
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        const Spacer(),
      ],
    ),
  );
}
