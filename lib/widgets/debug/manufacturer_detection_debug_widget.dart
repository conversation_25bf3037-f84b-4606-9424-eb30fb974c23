import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';

/// Debug widget to display manufacturer detection information
///
/// This widget is only available in debug mode and provides comprehensive
/// information about the manufacturer detection system and its adjustments.
///
/// Usage: Add this widget to any debug screen or development panel
class ManufacturerDetectionDebugWidget extends StatelessWidget {
  const ManufacturerDetectionDebugWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    final deviceInfo = DesignSystem.getManufacturerDebugInfo();
    final colorScheme = Theme.of(context).colorScheme;
    final isReferenceDevice = DesignSystem.isReferenceDevice();

    return Card(
      margin: const EdgeInsets.all(DesignSystem.spaceM),
      elevation: DesignSystem.elevationS,
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.phone_android,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: DesignSystem.spaceS),
                Text(
                  'Manufacturer Detection Debug',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),

            const SizedBox(height: DesignSystem.spaceM),

            // Device Information
            _buildInfoSection(
              context,
              'Device Information',
              [
                _InfoItem(
                  'Device',
                  DesignSystem.getDeviceDescription(),
                  icon: Icons.smartphone,
                ),
                _InfoItem(
                  'Reference Device',
                  isReferenceDevice
                      ? 'YES (Pixel 9 Pro Standard)'
                      : 'NO (Adjusted for consistency)',
                  icon: isReferenceDevice ? Icons.check_circle : Icons.tune,
                  valueColor: isReferenceDevice ? Colors.green : Colors.orange,
                ),
                _InfoItem(
                  'Initialization Status',
                  deviceInfo['isInitialized']
                      ? 'Initialized'
                      : 'Not Initialized',
                  icon: deviceInfo['isInitialized']
                      ? Icons.check_circle
                      : Icons.error,
                  valueColor:
                      deviceInfo['isInitialized'] ? Colors.green : Colors.red,
                ),
              ],
            ),

            const SizedBox(height: DesignSystem.spaceM),

            // Manufacturer Adjustments
            _buildInfoSection(
              context,
              'Manufacturer Adjustments',
              [
                _InfoItem(
                  'Spacing Multiplier',
                  '${deviceInfo['spacingMultiplier']?.toStringAsFixed(3) ?? 'N/A'}x',
                  icon: Icons.space_bar,
                  description: 'Adjusts spacing between UI elements',
                ),
                _InfoItem(
                  'Font Weight Multiplier',
                  '${deviceInfo['fontWeightMultiplier']?.toStringAsFixed(3) ?? 'N/A'}x',
                  icon: Icons.text_fields,
                  description: 'Compensates for manufacturer font rendering',
                ),
                _InfoItem(
                  'Text Width Compensation',
                  '${deviceInfo['textWidthCompensationMultiplier']?.toStringAsFixed(3) ?? 'N/A'}x',
                  icon: Icons.format_size,
                  description:
                      'Compensates for text width changes from font weight',
                ),
                _InfoItem(
                  'Icon Size Multiplier',
                  '${deviceInfo['iconSizeMultiplier']?.toStringAsFixed(3) ?? 'N/A'}x',
                  icon: Icons.image,
                  description: 'Ensures consistent icon appearance',
                ),
                _InfoItem(
                  'Elevation Multiplier',
                  '${deviceInfo['elevationMultiplier']?.toStringAsFixed(3) ?? 'N/A'}x',
                  icon: Icons.layers,
                  description: 'Adjusts shadow rendering for consistency',
                ),
              ],
            ),

            const SizedBox(height: DesignSystem.spaceM),

            // Visual Demonstration
            _buildVisualDemo(context),

            const SizedBox(height: DesignSystem.spaceS),

            // Footer
            Container(
              padding: const EdgeInsets.all(DesignSystem.spaceS),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(DesignSystem.radiusS),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: DesignSystem.spaceXS),
                  Expanded(
                    child: Text(
                      'This debug widget is only visible in debug mode and shows how your app adapts to different manufacturers.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    List<_InfoItem> items,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: DesignSystem.spaceS),
        ...items.map((item) => _buildInfoItem(context, item)),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, _InfoItem item) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: DesignSystem.spaceXS),
      child: Row(
        children: [
          Icon(
            item.icon,
            size: 16,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: DesignSystem.spaceS),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                ),
                if (item.description != null)
                  Text(
                    item.description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                  ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              item.value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: item.valueColor ?? colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVisualDemo(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final deviceInfo = DesignSystem.getManufacturerDebugInfo();
    final spacingMultiplier =
        (deviceInfo['spacingMultiplier'] ?? 1.0) as double;
    final iconMultiplier = (deviceInfo['iconSizeMultiplier'] ?? 1.0) as double;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Visual Demonstration',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: DesignSystem.spaceS),
        Container(
          padding: const EdgeInsets.all(DesignSystem.spaceM),
          decoration: BoxDecoration(
            border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(DesignSystem.radiusS),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Adjusted icon
              Icon(
                Icons.book,
                size: 24 * iconMultiplier,
                color: colorScheme.primary,
              ),
              SizedBox(width: 8 * spacingMultiplier),
              // Adjusted text
              Text(
                'Adjusted',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              SizedBox(width: 8 * spacingMultiplier),
              // Another adjusted icon
              Icon(
                Icons.star,
                size: 24 * iconMultiplier,
                color: colorScheme.secondary,
              ),
            ],
          ),
        ),
        const SizedBox(height: DesignSystem.spaceXS),
        Text(
          'This demo shows how spacing and icons are adjusted for your device.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
        ),
      ],
    );
  }
}

class _InfoItem {
  final String label;
  final String value;
  final IconData icon;
  final String? description;
  final Color? valueColor;

  const _InfoItem(
    this.label,
    this.value, {
    required this.icon,
    this.description,
    this.valueColor,
  });
}
