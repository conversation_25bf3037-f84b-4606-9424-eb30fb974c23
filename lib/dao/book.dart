import 'package:dasso_reader/dao/database.dart';
import 'package:dasso_reader/models/book.dart';

Future<int> insertBook(Book book) async {
  if (book.id != -1) {
    updateBook(book);
    return book.id;
  }
  final db = await DBHelper().database;
  return db.insert('tb_books', book.toMap());
}

Future<List<Book>> selectBooks() async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps =
      await db.query('tb_books', orderBy: 'update_time DESC');
  return List.generate(maps.length, (i) {
    return Book(
      id: maps[i]['id'] as int,
      title: maps[i]['title'] as String,
      coverPath: maps[i]['cover_path'] as String,
      filePath: maps[i]['file_path'] as String,
      lastReadPosition: maps[i]['last_read_position'] as String,
      readingPercentage: maps[i]['reading_percentage'] as double,
      author: maps[i]['author'] as String,
      isDeleted: maps[i]['is_deleted'] == 1 ? true : false,
      description: maps[i]['description'] as String?,
      rating: (maps[i]['rating'] as double?) ?? 0.0,
      groupId: maps[i]['group_id'] as int,
      createTime: DateTime.parse(maps[i]['create_time'] as String),
      updateTime: DateTime.parse(maps[i]['update_time'] as String),
    );
  });
}

Future<List<Book>> selectNotDeleteBooks() {
  return selectBooks().then((books) {
    return books.where((book) => !book.isDeleted).toList();
  });
}

Future<void> updateBook(Book book) async {
  book.updateTime = DateTime.now();
  final db = await DBHelper().database;
  // AnxLog.info('dao: update book: ${book.toMap()}');
  await db.update(
    'tb_books',
    book.toMap(),
    where: 'id = ?',
    whereArgs: [book.id],
  );
}

Future<Book> selectBookById(int id) async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.query(
    'tb_books',
    where: 'id = ?',
    whereArgs: [id],
  );
  return Book(
    id: maps[0]['id'] as int,
    title: maps[0]['title'] as String,
    coverPath: maps[0]['cover_path'] as String,
    filePath: maps[0]['file_path'] as String,
    lastReadPosition: maps[0]['last_read_position'] as String,
    readingPercentage: maps[0]['reading_percentage'] as double,
    author: maps[0]['author'] as String,
    isDeleted: maps[0]['is_deleted'] == 1 ? true : false,
    description: maps[0]['description'] as String?,
    rating: (maps[0]['rating'] as double?) ?? 0.0,
    groupId: maps[0]['group_id'] as int,
    createTime: DateTime.parse(maps[0]['create_time'] as String),
    updateTime: DateTime.parse(maps[0]['update_time'] as String),
  );
}

Future<List<String>> getCurrentBooks() async {
  final books = await selectNotDeleteBooks();
  return books.map((book) => book.filePath).toList();
}

Future<List<String>> getCurrentCover() async {
  final books = await selectNotDeleteBooks();
  return books.map((book) => book.coverPath).toList();
}
